/*

Gradient Light (c) <PERSON><PERSON> <<EMAIL>>

*/

.hljs
{
display: block;
overflow-x: auto;
padding: 0.5em;
background: rgb(255,253,141);
background: linear-gradient(142deg, rgba(255,253,141,1) 0%, rgba(252,183,255,1) 35%, rgba(144,236,255,1) 100%);
color:#250482;
}

.hljs-subtr{
color:#01958B;
}

.hljs-doctag,
.hljs-meta,
.hljs-comment,
.hljs-quote
{
  color:#CB7200;
}

.hljs-selector-tag,
.hljs-selector-id,
.hljs-template-tag,
.hljs-regexp,
.hljs-attr,
.hljs-tag
{
  color:#07BD5F;
}

.hljs-params,
.hljs-selector-class,
.hljs-bullet

{
  color:#43449F;
  
}

.hljs-keyword,
.hljs-section,
.hljs-meta-keyword,
.hljs-symbol,
.hljs-type

{

  color:#7D2801;
}

.hljs-addition,
.hljs-number,
.hljs-link
{
  color:#7F0096;
}


.hljs-string
{
  color: #38c0ff;
}


.hljs-attribute,
.hljs-addition
{
  color:#296562;
}

.hljs-variable,
.hljs-template-variable

{
  color:#025C8F;
}

.hljs-builtin-name,
.hljs-built_in,
.hljs-formula,
.hljs-name,
.hljs-title,
.hljs-class,
.hljs-function
{
  color: #529117;

}

.hljs-selector-pseudo,
.hljs-deletion,
.hljs-literal
{
  color:#AD13FF;

}

.hljs-emphasis,
.hljs-quote
{
  font-style:italic;
}

.hljs-params,
.hljs-selector-class,
.hljs-strong,
.hljs-selector-tag,
.hljs-selector-id,
.hljs-template-tag,
.hljs-section,
.hljs-keyword
{
  font-weight:bold;
}








